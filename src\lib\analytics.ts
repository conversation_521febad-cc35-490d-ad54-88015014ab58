"use client"

// Analytics tracking for resume downloads and other events
export const trackEvent = (eventName: string, properties?: Record<string, unknown>) => {
  // In a real application, you would integrate with analytics services like:
  // - Google Analytics 4
  // - Mixpanel
  // - Amplitude
  // - PostHog
  
  console.log("Analytics Event:", eventName, properties)
  
  // Example Google Analytics 4 tracking (if gtag is available)
  if (typeof window !== "undefined" && (window as unknown as { gtag?: unknown }).gtag) {
    const gtag = (window as unknown as { gtag: (...args: unknown[]) => void }).gtag
    gtag("event", eventName, properties)
  }
  
  // Example for custom analytics endpoint
  if (typeof window !== "undefined") {
    fetch("/api/analytics", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        event: eventName,
        properties: {
          ...properties,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: window.location.href,
        },
      }),
    }).catch(console.error)
  }
}

export const trackResumeDownload = () => {
  trackEvent("resume_download", {
    source: "portfolio_website",
    format: "pdf",
  })
}

export const trackProjectView = (projectId: string, projectName: string) => {
  trackEvent("project_view", {
    project_id: projectId,
    project_name: projectName,
  })
}

export const trackContactFormSubmit = () => {
  trackEvent("contact_form_submit", {
    form_type: "contact",
  })
}

export const trackSectionView = (sectionName: string) => {
  trackEvent("section_view", {
    section: sectionName,
  })
}

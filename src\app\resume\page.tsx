import { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { ArrowLeft, Download, Mail, Phone, MapPin, Github, Linkedin } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { ResumeDownload } from "@/components/resume-download"

export const metadata: Metadata = {
  title: "Resume - Sameer Dev",
  description: "Professional resume of Sameer Dev - Full Stack Developer",
}

export default function ResumePage() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="border-b bg-background/80 backdrop-blur-md sticky top-0 z-10">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <Button variant="ghost" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Portfolio
            </Link>
          </Button>
          <ResumeDownload showIcon={false} />
        </div>
      </header>

      {/* Resume Content */}
      <main className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="bg-background border rounded-lg p-8 shadow-sm">
          {/* Header Section */}
          <div className="text-center mb-8 pb-8 border-b">
            <h1 className="text-4xl font-bold mb-2">Sameer Dev</h1>
            <p className="text-xl text-muted-foreground mb-4">
              Full Stack Developer & AI Integration Specialist
            </p>
            
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center gap-1">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>San Francisco, CA</span>
              </div>
              <div className="flex items-center gap-1">
                <Github className="h-4 w-4" />
                <span>github.com/sameerdev</span>
              </div>
              <div className="flex items-center gap-1">
                <Linkedin className="h-4 w-4" />
                <span>linkedin.com/in/sameerdev</span>
              </div>
            </div>
          </div>

          {/* Professional Summary */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-primary">Professional Summary</h2>
            <p className="text-muted-foreground leading-relaxed">
              Passionate Full Stack Developer with 3+ years of experience in building modern web applications 
              using the MERN stack. Specialized in AI integration, secure authentication systems, and creating 
              exceptional user experiences. Proven track record of delivering high-quality projects on time and 
              within budget, with a focus on scalable solutions and clean code architecture.
            </p>
          </section>

          {/* Technical Skills */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-primary">Technical Skills</h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold mb-2">Frontend</h3>
                <p className="text-muted-foreground text-sm">
                  React, Next.js, TypeScript, Tailwind CSS, Framer Motion, HTML/CSS
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Backend</h3>
                <p className="text-muted-foreground text-sm">
                  Node.js, Express.js, MongoDB, PostgreSQL, Redis, GraphQL
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">AI/ML</h3>
                <p className="text-muted-foreground text-sm">
                  OpenAI API, Python, TensorFlow, Natural Language Processing
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">DevOps & Tools</h3>
                <p className="text-muted-foreground text-sm">
                  Git, Docker, AWS, Vercel, CI/CD, Jest, Prisma
                </p>
              </div>
            </div>
          </section>

          {/* Experience */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-primary">Professional Experience</h2>
            
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-semibold">Senior Full Stack Developer</h3>
                    <p className="text-muted-foreground">TechCorp Inc.</p>
                  </div>
                  <span className="text-sm text-muted-foreground">2022 - Present</span>
                </div>
                <ul className="list-disc list-inside text-muted-foreground text-sm space-y-1">
                  <li>Developed AI-powered e-commerce platform increasing conversion rates by 40%</li>
                  <li>Built secure authentication microservice handling 10,000+ daily users</li>
                  <li>Led team of 3 developers in implementing real-time features using WebSocket</li>
                  <li>Optimized application performance reducing load times by 60%</li>
                </ul>
              </div>

              <div>
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <h3 className="font-semibold">Full Stack Developer</h3>
                    <p className="text-muted-foreground">StartupXYZ</p>
                  </div>
                  <span className="text-sm text-muted-foreground">2021 - 2022</span>
                </div>
                <ul className="list-disc list-inside text-muted-foreground text-sm space-y-1">
                  <li>Created feedback management system with AI-powered sentiment analysis</li>
                  <li>Implemented responsive designs improving mobile engagement by 60%</li>
                  <li>Optimized database queries reducing server response times by 50%</li>
                  <li>Collaborated with design team to create intuitive user interfaces</li>
                </ul>
              </div>
            </div>
          </section>

          {/* Key Projects */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-primary">Key Projects</h2>
            
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">AI-Powered E-commerce Platform</h3>
                <p className="text-sm text-muted-foreground mb-1">
                  React, Node.js, MongoDB, OpenAI API, Stripe
                </p>
                <p className="text-muted-foreground text-sm">
                  Built intelligent product recommendations and search functionality with payment processing
                </p>
              </div>

              <div>
                <h3 className="font-semibold">Smart Feedback Management System</h3>
                <p className="text-sm text-muted-foreground mb-1">
                  Next.js, TypeScript, PostgreSQL, TensorFlow
                </p>
                <p className="text-muted-foreground text-sm">
                  Real-time sentiment analysis and automated customer insights generation
                </p>
              </div>

              <div>
                <h3 className="font-semibold">Multi-vendor Marketplace</h3>
                <p className="text-sm text-muted-foreground mb-1">
                  React, Node.js, MongoDB, AWS
                </p>
                <p className="text-muted-foreground text-sm">
                  Complete marketplace solution with vendor management and analytics dashboard
                </p>
              </div>
            </div>
          </section>

          {/* Education */}
          <section className="mb-8">
            <h2 className="text-2xl font-bold mb-4 text-primary">Education</h2>
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-semibold">Bachelor of Science in Computer Science</h3>
                <p className="text-muted-foreground">University of California, Berkeley</p>
              </div>
              <span className="text-sm text-muted-foreground">2021</span>
            </div>
          </section>

          {/* Certifications */}
          <section>
            <h2 className="text-2xl font-bold mb-4 text-primary">Certifications</h2>
            <div className="grid md:grid-cols-2 gap-2">
              <div className="text-muted-foreground text-sm">• AWS Certified Developer</div>
              <div className="text-muted-foreground text-sm">• MongoDB Certified Developer</div>
              <div className="text-muted-foreground text-sm">• Google Cloud Professional</div>
              <div className="text-muted-foreground text-sm">• Meta React Developer</div>
            </div>
          </section>
        </div>
      </main>
    </div>
  )
}

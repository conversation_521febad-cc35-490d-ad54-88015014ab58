import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { StructuredData } from "@/components/structured-data";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Sameer Dev - Full Stack Developer",
  description: "Professional portfolio of Sameer Dev - Full Stack Developer specializing in MERN stack, AI integration, and secure authentication systems.",
  keywords: ["Full Stack Developer", "MERN Stack", "React", "Node.js", "AI Integration", "Web Development"],
  authors: [{ name: "Same<PERSON> Dev" }],
  creator: "Sameer Dev",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://sameerdev.vercel.app",
    title: "Sameer Dev - Full Stack Developer",
    description: "Professional portfolio of Sameer Dev - Full Stack Developer specializing in MERN stack, AI integration, and secure authentication systems.",
    siteName: "Sameer Dev Portfolio",
  },
  twitter: {
    card: "summary_large_image",
    title: "Sameer Dev - Full Stack Developer",
    description: "Professional portfolio of Sameer Dev - Full Stack Developer specializing in MERN stack, AI integration, and secure authentication systems.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <StructuredData />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}

export function StructuredData() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": "<PERSON><PERSON>",
    "jobTitle": "Full Stack Developer",
    "description": "Full Stack Developer specializing in MERN stack, AI integration, and secure authentication systems",
    "url": "https://sameerdev.vercel.app",
    "sameAs": [
      "https://github.com/sameerdev",
      "https://linkedin.com/in/sameerdev"
    ],
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "San Francisco",
      "addressRegion": "CA",
      "addressCountry": "US"
    },
    "email": "<EMAIL>",
    "telephone": "******-123-4567",
    "knowsAbout": [
      "React",
      "Next.js",
      "Node.js",
      "MongoDB",
      "TypeScript",
      "AI Integration",
      "Authentication Systems",
      "Full Stack Development"
    ],
    "hasOccupation": {
      "@type": "Occupation",
      "name": "Full Stack Developer",
      "description": "Develops web applications using modern technologies",
      "skills": [
        "React",
        "Next.js",
        "Node.js",
        "MongoDB",
        "PostgreSQL",
        "TypeScript",
        "Tailwind CSS",
        "AI Integration",
        "Authentication Systems"
      ]
    },
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "University of California, Berkeley"
    },
    "award": [
      "AWS Certified Developer",
      "MongoDB Certified Developer",
      "Google Cloud Professional",
      "Meta React Developer"
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  )
}

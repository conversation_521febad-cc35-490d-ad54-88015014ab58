"use client"

import * as React from "react"
import { Download, FileText, ExternalLink } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { trackResumeDownload } from "@/lib/analytics"

interface ResumeDownloadProps {
  variant?: "default" | "outline" | "ghost"
  size?: "default" | "sm" | "lg"
  showIcon?: boolean
  className?: string
}

export function ResumeDownload({ 
  variant = "default", 
  size = "default", 
  showIcon = true,
  className 
}: ResumeDownloadProps) {
  const handleDownload = async () => {
    try {
      // Track the download event
      trackResumeDownload()
      
      // In a real application, you would have an actual PDF file
      // For now, we'll create a simple text file as a placeholder
      const resumeContent = `
SAMEER DEV
Full Stack Developer & AI Integration Specialist

CONTACT INFORMATION
Email: <EMAIL>
Phone: +****************
Location: San Francisco, CA
LinkedIn: linkedin.com/in/sameerdev
GitHub: github.com/sameerdev

PROFESSIONAL SUMMARY
Passionate Full Stack Developer with 3+ years of experience in building modern web applications using the MERN stack. Specialized in AI integration, secure authentication systems, and creating exceptional user experiences. Proven track record of delivering high-quality projects on time and within budget.

TECHNICAL SKILLS
Frontend: React, Next.js, TypeScript, Tailwind CSS, Framer Motion
Backend: Node.js, Express.js, MongoDB, PostgreSQL, Redis
AI/ML: OpenAI API, Python, TensorFlow, Natural Language Processing
DevOps: Git, Docker, AWS, Vercel, CI/CD
Tools: Jest, Prisma, GraphQL, Socket.io

EXPERIENCE
Senior Full Stack Developer | TechCorp Inc. | 2022 - Present
• Developed AI-powered e-commerce platform increasing conversion rates by 40%
• Built secure authentication microservice handling 10,000+ daily users
• Led team of 3 developers in implementing real-time features

Full Stack Developer | StartupXYZ | 2021 - 2022
• Created feedback management system with sentiment analysis
• Implemented responsive designs improving mobile engagement by 60%
• Optimized database queries reducing load times by 50%

PROJECTS
AI-Powered E-commerce Platform
• React, Node.js, MongoDB, OpenAI API
• Intelligent product recommendations and search
• Stripe payment integration

Smart Feedback Management System
• Next.js, TypeScript, PostgreSQL, TensorFlow
• Real-time sentiment analysis and reporting
• Automated customer insights generation

EDUCATION
Bachelor of Science in Computer Science
University of California, Berkeley | 2021

CERTIFICATIONS
• AWS Certified Developer
• MongoDB Certified Developer
• Google Cloud Professional
• Meta React Developer

ACHIEVEMENTS
• 100% client satisfaction rate
• 50+ successful projects delivered
• Open source contributor with 1000+ GitHub stars
• Speaker at 3 tech conferences
      `.trim()

      // Create and download the file
      const blob = new Blob([resumeContent], { type: 'text/plain' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = 'Sameer_Dev_Resume.txt'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      // Show success message (you could use a toast notification here)
      console.log("Resume downloaded successfully!")
      
    } catch (error) {
      console.error("Error downloading resume:", error)
      // Show error message (you could use a toast notification here)
    }
  }

  const handleViewOnline = () => {
    // In a real application, this would open a PDF viewer or dedicated resume page
    window.open("/resume", "_blank")
  }

  return (
    <div className={`flex gap-2 ${className}`}>
      <Button
        variant={variant}
        size={size}
        onClick={handleDownload}
        className="group"
      >
        {showIcon && <Download className="h-4 w-4 mr-2 group-hover:translate-y-1 transition-transform" />}
        Download Resume
      </Button>
      
      <Button
        variant="outline"
        size={size}
        onClick={handleViewOnline}
        className="group"
      >
        <FileText className="h-4 w-4 mr-2" />
        View Online
        <ExternalLink className="h-3 w-3 ml-1 group-hover:translate-x-1 transition-transform" />
      </Button>
    </div>
  )
}
